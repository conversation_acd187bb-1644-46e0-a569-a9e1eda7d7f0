import { Stack } from 'expo-router';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';
import { CustomAppbar } from '@/components/navigation/CustomAppbar';

export default function SettingsLayout() {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <Stack
      screenOptions={{
        headerShown: true,
        header: (props) => <CustomAppbar {...props} />,
      }}
    >
      {/* Define screens within the settings stack */}
      {/* The actual screen content comes from app/(settings)/index.tsx */}
      <Stack.Screen
        name="index"
        options={{
          title: t('settings'), // Get title from translation
        }}
      />
    </Stack>
  );
}

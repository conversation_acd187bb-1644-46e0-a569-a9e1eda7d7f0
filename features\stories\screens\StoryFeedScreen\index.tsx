import React, { useState, useCallback, useRef } from 'react';
import { View, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Surface, Appbar, FAB, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { StorySegment } from '@/api/stories/types';
import { useRouter } from 'expo-router';

// Hooks
import { useStoryDetails } from '../../hooks/useStoryDetails';
import { useStoryFeed } from '../../hooks/useStoryFeed';

// Components
import StoryFeed from '../../components/StoryFeed';
import BreadcrumbNavigation from '../../components/BreadcrumbNavigation';
import { LoadingState } from '../StoryDetailScreen/LoadingState';
import { ErrorState } from '../StoryDetailScreen/ErrorState';
import { EmptyState } from '../StoryDetailScreen/EmptyState';

interface StoryFeedScreenProps {
  storyId: string;
}

export default function StoryFeedScreen({ storyId }: StoryFeedScreenProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();
  const router = useRouter();
  const scrollViewRef = useRef<ScrollView>(null);

  // 添加状态来控制是否显示创作图标
  const [showCreationIcons, setShowCreationIcons] = useState(false);

  // Get story details
  const {
    story,
    isLoading: isLoadingDetails,
    error: storyError,
    fetchStoryDetails,
  } = useStoryDetails(storyId);

  // Get story feed with virtualization and pagination
  const {
    segments,
    isLoading: isLoadingSegments,
    error: segmentsError,
    hasMoreSegments,
    loadMore: loadMoreSegments,
    refresh: refreshSegments,
    navigateToBranch,
    currentPath,
  } = useStoryFeed({
    storyId,
    pageSize: 10,
  });

  // Handle branch selection
  const handleBranchSelect = useCallback(
    async (segmentId: string) => {
      await navigateToBranch(segmentId);
      // Scroll to top after branch selection
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    },
    [navigateToBranch]
  );

  // Handle path selection in breadcrumb
  const handlePathSelect = useCallback(
    (index: number) => {
      if (index === -1) {
        // Navigate to story root
        navigateToBranch(segments[0]?.id || '');
      } else if (index >= 0 && index < currentPath.length) {
        // Navigate to specific path segment
        navigateToBranch(currentPath[index]);
      }
    },
    [currentPath, navigateToBranch, segments]
  );

  // Handle back button press
  const handleBackPress = useCallback(() => {
    router.back();
  }, [router]);

  // Handle FAB button press - toggle creation icons
  const handleFabPress = useCallback(() => {
    setShowCreationIcons((prev) => !prev);
  }, []);

  // Handle create same-level segment
  const handleCreateSameLevelSegment = useCallback(
    (parentSegmentId: string) => {
      router.push({
        pathname: '/create-segment',
        params: {
          storyId,
          parentSegmentId,
        },
      });
    },
    [router, storyId]
  );

  // Handle create child segment
  const handleCreateChildSegment = useCallback(
    (parentSegmentId: string) => {
      router.push({
        pathname: '/create-segment',
        params: {
          storyId,
          parentSegmentId,
        },
      });
    },
    [router, storyId]
  );

  // Show loading state
  if (isLoadingDetails && !story) {
    return <LoadingState />;
  }

  // Show error state
  if (storyError) {
    return <ErrorState error={storyError} onRetry={fetchStoryDetails} />;
  }

  // Show empty state
  if (!story) {
    return <EmptyState />;
  }

  // Generate path names for breadcrumb
  const pathNames =
    currentPath && Array.isArray(currentPath)
      ? currentPath.map((pathId) => {
          const segment = segments.find((s) => s.id === pathId);
          if (!segment) return t('storyDetail.branch', 'Branch');

          // Extract branch title if available
          const titleMatch = segment.content.match(/^\[(.*?)\]\s/);
          return titleMatch ? titleMatch[1] : t('storyDetail.branch', 'Branch');
        })
      : [];

  return (
    <Surface style={{ flex: 1 }}>
      {/* Header */}
      <Appbar.Header>
        <Appbar.BackAction onPress={handleBackPress} />
        <Appbar.Content title={story.title} />
        <Appbar.Action
          icon="chat-outline"
          onPress={() => router.push(`/stories/${storyId}/comments`)}
        />
      </Appbar.Header>

      {/* Breadcrumb Navigation */}
      <BreadcrumbNavigation
        storyTitle={story.title}
        currentPath={currentPath}
        pathNames={pathNames}
        onPathSelect={handlePathSelect}
      />

      {/* Story Feed */}
      <View style={{ flex: 1 }}>
        <StoryFeed
          segments={segments}
          isLoading={isLoadingSegments}
          onRefresh={refreshSegments}
          onLoadMore={loadMoreSegments}
          onBranchSelect={handleBranchSelect}
          hasMoreSegments={hasMoreSegments}
          showCreationIcons={showCreationIcons}
          onCreateSameLevelSegment={handleCreateSameLevelSegment}
          onCreateChildSegment={handleCreateChildSegment}
        />
      </View>

      {/* Floating Action Button Group */}
      <FAB.Group
        open={showCreationIcons}
        visible
        icon={showCreationIcons ? 'close' : 'feather'}
        actions={[
          {
            icon: 'plus-circle-outline',
            label: t('storyDetail.createSameLevel', '创建同级'),
            onPress: () => {
              const lastSegment = segments[segments.length - 1];
              if (lastSegment) {
                handleCreateSameLevelSegment(
                  lastSegment.parent_segment_id || ''
                );
              }
            },
            style: { backgroundColor: theme.colors.tertiary },
          },
          {
            icon: 'arrow-down-circle-outline',
            label: t('storyDetail.createChild', '创建下级'),
            onPress: () => {
              const lastSegment = segments[segments.length - 1];
              if (lastSegment) {
                handleCreateChildSegment(lastSegment.id);
              }
            },
            style: { backgroundColor: theme.colors.secondary },
          },
        ]}
        onStateChange={({ open }) => setShowCreationIcons(open)}
        onPress={handleFabPress}
      />
    </Surface>
  );
}

import React from 'react';
import { ViewStyle } from 'react-native';
import FeaturedStoryCard from '@/components/stories/FeaturedStoryCard';
import { Story } from '@/api/stories';

interface FeaturedStoryProps {
  story: Story;
  onPress?: (storyId: string) => void;
  style?: ViewStyle;
}

export function FeaturedStory({ story, onPress, style }: FeaturedStoryProps) {
  // Handle case where story might be null or undefined
  if (!story) {
    return null;
  }

  return (
    <FeaturedStoryCard
      story={story}
      onPress={() => onPress?.(story.id)}
      style={style}
    />
  );
}

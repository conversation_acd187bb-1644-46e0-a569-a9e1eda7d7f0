import React from 'react';
import { ScrollView, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Surface, Text, TouchableRipple, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface BreadcrumbNavigationProps {
  storyTitle: string;
  currentPath: string[];
  pathNames?: string[];
  onPathSelect: (index: number) => void;
}

export default function BreadcrumbNavigation({
  storyTitle,
  currentPath,
  pathNames = [],
  onPathSelect,
}: BreadcrumbNavigationProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  // Ensure pathNames has the same length as currentPath
  const displayNames =
    currentPath &&
    Array.isArray(currentPath) &&
    pathNames.length === currentPath.length
      ? pathNames
      : currentPath && Array.isArray(currentPath)
      ? currentPath.map((_, index) =>
          index === 0
            ? t('storyDetail.mainBranch', '主线')
            : t('storyDetail.branch', '分支') + ' ' + (index + 1)
        )
      : [];

  return (
    <Surface
      style={{
        paddingHorizontal: theme.spacing?.md || 16,
        paddingVertical: theme.spacing?.sm || 8,
        elevation: 0,
      }}
    >
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: theme.spacing?.sm || 8,
        }}
      >
        {/* Story Title */}
        <TouchableRipple
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: theme.spacing?.sm || 8,
            paddingVertical: theme.spacing?.xs || 4,
            borderRadius: theme.roundness || 8,
          }}
          onPress={() => onPathSelect(-1)} // -1 indicates the story root
        >
          <>
            <Icon source="home" size={16} />
            <Text
              variant="labelMedium"
              numberOfLines={1}
              style={{
                marginLeft: theme.spacing?.xs || 4,
                color: theme.colors.onSurface,
              }}
            >
              {storyTitle}
            </Text>
          </>
        </TouchableRipple>

        {/* Path Items */}
        {currentPath &&
          Array.isArray(currentPath) &&
          currentPath.map((pathId, index) => (
            <React.Fragment key={pathId}>
              <Icon
                source="chevron-right"
                size={16}
                iconColor={theme.colors.onSurfaceVariant}
              />
              <TouchableRipple
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: theme.spacing?.sm || 8,
                  paddingVertical: theme.spacing?.xs || 4,
                  borderRadius: theme.roundness || 8,
                  backgroundColor:
                    index === currentPath.length - 1
                      ? theme.colors.primaryContainer
                      : 'transparent',
                }}
                onPress={() => onPathSelect(index)}
              >
                <Text
                  variant="labelMedium"
                  numberOfLines={1}
                  style={{
                    color:
                      index === currentPath.length - 1
                        ? theme.colors.onPrimaryContainer
                        : theme.colors.onSurface,
                  }}
                >
                  {displayNames[index]}
                </Text>
              </TouchableRipple>
            </React.Fragment>
          ))}
      </ScrollView>
    </Surface>
  );
}

import React, { useState, useRef, useEffect } from 'react';
import { View, FlatList, Animated, Dimensions } from 'react-native';
import { useTranslation } from 'react-i18next';
import {
  Surface,
  Text,
  ActivityIndicator,
  IconButton,
  Chip,
  Button,
  Icon,
} from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import BranchPreviewCard from './BranchPreviewCard';
import { StorySegment } from '@/api/stories/types';

export interface BranchCarouselProps {
  segmentId: string;
  branches: StorySegment[];
  totalBranches: number;
  isLoading: boolean;
  currentPage: number;
  hasMorePages: boolean;
  onBranchSelect: (branchId: string) => void;
  onClose: () => void;
  onLoadMore: () => void;
  onFilterChange: (filter: 'popular' | 'sameAuthor' | 'recommended') => void;
}

export default function BranchCarousel({
  segmentId,
  branches,
  totalBranches,
  isLoading,
  currentPage,
  hasMorePages,
  onBranchSelect,
  onClose,
  onLoadMore,
  onFilterChange,
}: BranchCarouselProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  // State for tracking the current visible branch index
  const [currentIndex, setCurrentIndex] = useState(0);
  const [activeFilter, setActiveFilter] = useState<
    'popular' | 'sameAuthor' | 'recommended'
  >('popular');

  // Ref for the FlatList
  const flatListRef = useRef<FlatList>(null);

  // Animation for the carousel appearance
  const slideAnim = useRef(new Animated.Value(0)).current;

  // Screen dimensions
  const { width } = Dimensions.get('window');

  // Start animation when component mounts
  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  // Handle filter change
  const handleFilterChange = (
    filter: 'popular' | 'sameAuthor' | 'recommended'
  ) => {
    setActiveFilter(filter);
    setCurrentIndex(0);
    onFilterChange(filter);
  };

  // Handle scroll end to update current index
  const handleScrollEnd = (e: any) => {
    const contentOffsetX = e.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / (width * 0.8)); // Assuming each card takes 80% of screen width
    setCurrentIndex(index);
  };

  // Handle branch selection
  const handleBranchSelect = (branchId: string) => {
    // Close carousel with animation
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      onBranchSelect(branchId);
    });
  };

  // Handle close
  const handleClose = () => {
    // Close carousel with animation
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(onClose);
  };

  // Handle end reached to load more branches
  const handleEndReached = () => {
    if (!isLoading && hasMorePages) {
      onLoadMore();
    }
  };

  // Render the filter button
  const renderFilterButton = (
    label: string,
    filter: 'popular' | 'sameAuthor' | 'recommended'
  ) => (
    <Chip
      mode={activeFilter === filter ? 'flat' : 'outlined'}
      onPress={() => handleFilterChange(filter)}
      style={{ marginRight: theme.spacing?.xs || 4 }}
    >
      {label}
    </Chip>
  );

  // Render the branch preview card
  const renderBranchItem = ({
    item,
    index,
  }: {
    item: StorySegment;
    index: number;
  }) => (
    <BranchPreviewCard
      branch={item}
      onPress={() => handleBranchSelect(item.id)}
      isActive={index === currentIndex}
    />
  );

  // Render footer with "View All" link if needed
  const renderFooter = () => {
    if (isLoading) {
      return (
        <View
          style={{
            padding: theme.spacing?.lg || 24,
            alignItems: 'center',
          }}
        >
          <ActivityIndicator size="small" />
        </View>
      );
    }

    if (totalBranches > branches.length && hasMorePages) {
      return (
        <View
          style={{
            padding: theme.spacing?.lg || 24,
            alignItems: 'center',
          }}
        >
          <Button
            mode="text"
            icon="arrow-right"
            contentStyle={{ flexDirection: 'row-reverse' }}
            onPress={() => {
              // 加载更多分支
              onLoadMore();
            }}
          >
            {t('storyDetail.viewAllBranches', '查看全部 {{count}} 个分支', {
              count: totalBranches,
            })}
          </Button>
        </View>
      );
    }

    return null;
  };

  return (
    <Animated.View
      style={{
        opacity: slideAnim,
        transform: [
          {
            translateY: slideAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [50, 0],
            }),
          },
        ],
      }}
    >
      <Surface
        style={{
          margin: theme.spacing?.md || 16,
          borderRadius: theme.roundness || 12,
          overflow: 'hidden',
        }}
        elevation={4}
      >
        {/* Top Control Bar */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: theme.spacing?.md || 16,
            backgroundColor: theme.colors.surfaceVariant,
          }}
        >
          {/* Left - Filter Tabs */}
          <View style={{ flexDirection: 'row', flex: 1 }}>
            {renderFilterButton(t('storyDetail.popular', '最火'), 'popular')}
            {renderFilterButton(
              t('storyDetail.sameAuthor', '同一作者'),
              'sameAuthor'
            )}
            {renderFilterButton(
              t('storyDetail.recommended', '推荐'),
              'recommended'
            )}
          </View>

          {/* Right - Status and Close */}
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text
              variant="bodySmall"
              style={{
                marginRight: theme.spacing?.sm || 8,
                color: theme.colors.onSurfaceVariant,
              }}
            >
              {branches.length > 0
                ? `${currentIndex + 1} / ${totalBranches}`
                : `0 / 0`}
            </Text>
            <IconButton icon="close" size={20} onPress={handleClose} />
          </View>
        </View>

        {/* Branch Preview Cards */}
        {isLoading && branches.length === 0 ? (
          <View
            style={{
              padding: theme.spacing?.xl || 32,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <ActivityIndicator size="large" />
          </View>
        ) : branches.length > 0 ? (
          <FlatList
            ref={flatListRef}
            data={branches}
            renderItem={renderBranchItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              paddingHorizontal: theme.spacing?.md || 16,
              paddingVertical: theme.spacing?.sm || 8,
            }}
            snapToInterval={width * 0.8} // Snap to card width (80% of screen)
            snapToAlignment="center"
            decelerationRate="fast"
            onMomentumScrollEnd={handleScrollEnd}
            onEndReached={handleEndReached}
            onEndReachedThreshold={0.5}
            initialNumToRender={3}
            maxToRenderPerBatch={3}
            windowSize={5}
            removeClippedSubviews={true}
            ListFooterComponent={renderFooter}
          />
        ) : (
          <View
            style={{
              padding: theme.spacing?.xl || 32,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Icon source="source-branch" size={24} />
            <Text
              variant="bodyMedium"
              style={{
                marginTop: theme.spacing?.sm || 8,
                color: theme.colors.onSurfaceVariant,
                textAlign: 'center',
              }}
            >
              {t(
                'storyDetail.noBranches',
                '暂无分支，点击 + 按钮创建第一个分支'
              )}
            </Text>
          </View>
        )}
      </Surface>
    </Animated.View>
  );
}

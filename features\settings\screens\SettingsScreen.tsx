import React from 'react';
import { ScrollView, Alert } from 'react-native';
import { Text, Button, Surface } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';
// Import the new child components
import { ThemeOptionsGroup } from '../components/ThemeOptionsGroup';
import { LanguageOptionsGroup } from '../components/LanguageOptionsGroup';
import { signOut as supabaseSignOut } from '@/api/auth'; // Renamed to avoid conflict
import { useAuthStore } from '@/lib/store/authStore'; // For local state clear

// Rename component to SettingsScreen
export default function SettingsScreen() {
  const { t } = useTranslation();
  const theme = usePaperTheme();
  const clearAuthStore = useAuthStore((state) => state.signOut); // Get the signOut action

  const handleLogout = async () => {
    // First, clear local auth state
    clearAuthStore();

    // Then, sign out from Supabase
    const { error } = await supabaseSignOut();

    if (error) {
      console.error('Error signing out from Supabase:', error);
      Alert.alert(
        t('logoutErrorTitle', 'Logout Error'),
        error.message ||
          t('logoutErrorGeneric', 'Failed to sign out. Please try again.')
      );
    }
    // No explicit navigation needed here, as the RootLayout should detect
    // the absence of user/session and redirect to the login screen.
  };

  return (
    <Surface style={{ flex: 1 }}>
      {/* Header is now handled by _layout.tsx */}

      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          paddingBottom: theme.spacing?.xxl || 48,
        }}
        showsVerticalScrollIndicator={false}
      >
        {/* Theme Section */}
        <Surface
          style={{
            backgroundColor: theme.colors.surface,
            paddingTop: theme.spacing?.lg || 24,
          }}
          elevation={0}
        >
          <Text
            variant="titleMedium"
            style={{
              marginTop: theme.spacing?.lg || 24,
              marginBottom: theme.spacing?.sm || 8,
              marginHorizontal: theme.spacing?.md || 16,
              color: theme.colors.onSurface,
              fontWeight: '600',
            }}
          >
            {t('themeSettings')}
          </Text>
          <ThemeOptionsGroup />
        </Surface>

        {/* Language Section */}
        <Surface
          style={{
            backgroundColor: theme.colors.surface,
            paddingTop: theme.spacing?.md || 16,
          }}
          elevation={0}
        >
          <Text
            variant="titleMedium"
            style={{
              marginTop: theme.spacing?.lg || 24,
              marginBottom: theme.spacing?.sm || 8,
              marginHorizontal: theme.spacing?.md || 16,
              color: theme.colors.onSurface,
              fontWeight: '600',
            }}
          >
            {t('languageSettings')}
          </Text>
          <LanguageOptionsGroup />
        </Surface>

        {/* Account Section */}
        <Surface
          style={{
            backgroundColor: theme.colors.surface,
            paddingTop: theme.spacing?.md || 16,
          }}
          elevation={0}
        >
          <Text
            variant="titleMedium"
            style={{
              marginTop: theme.spacing?.lg || 24,
              marginBottom: theme.spacing?.sm || 8,
              marginHorizontal: theme.spacing?.md || 16,
              color: theme.colors.onSurface,
              fontWeight: '600',
            }}
          >
            {t('settingsDetails.accountSection', 'Account')}
          </Text>

          <Button
            mode="contained"
            buttonColor={theme.colors.error}
            textColor={theme.colors.onError}
            onPress={handleLogout}
            style={{
              marginTop: theme.spacing?.md || 16,
              marginHorizontal: theme.spacing?.md || 16,
              borderRadius: theme.roundness || 8,
            }}
            contentStyle={{
              paddingVertical: theme.spacing?.sm || 8,
            }}
          >
            {t('settingsDetails.account.logout', 'Log Out')}
          </Button>
        </Surface>
      </ScrollView>
    </Surface>
  );
}

// Settings screen is now fully Paper-ized with MD3 components

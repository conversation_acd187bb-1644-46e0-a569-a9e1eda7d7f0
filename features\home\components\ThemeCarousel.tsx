import React from 'react';
import { ScrollView } from 'react-native';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import ThemeCard, { Theme } from '@/components/stories/ThemeCard'; // Use existing card

interface ThemeCarouselProps {
  themes: Theme[];
  onThemePress?: (themeId: string) => void;
}

export function ThemeCarousel({ themes, onThemePress }: ThemeCarouselProps) {
  const theme = usePaperTheme();

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{
        paddingVertical: theme.spacing?.sm || 8,
        paddingLeft: theme.spacing?.md || 16,
        paddingRight: theme.spacing?.md || 16,
        gap: theme.spacing?.sm || 8, // MD3 Expressive 间距
      }}
      style={{
        flexGrow: 0, // 防止过度拉伸
      }}
    >
      {themes.map((themeItem) => (
        <ThemeCard
          key={themeItem.id}
          theme={themeItem}
          onPress={() => onThemePress?.(themeItem.id)}
        />
      ))}
    </ScrollView>
  );
}

import React, { useState, useRef } from 'react';
import { View, FlatList, RefreshControl } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Text, FAB, IconButton } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import StoryNodeCard from '../StoryNodeCard';
import BranchCarousel from '../BranchCarousel';
import { useBranchCarousel } from '../../hooks/useBranchCarousel';
import { StorySegment } from '@/api/stories/types';
import { getChildrenCount } from '@/utils/storyHelpers';

interface StoryFeedProps {
  segments: StorySegment[];
  isLoading: boolean;
  onRefresh: () => Promise<void>;
  onLoadMore: () => Promise<void>;
  onBranchSelect: (segmentId: string) => Promise<void>;
  hasMoreSegments: boolean;
  showCreationIcons?: boolean;
  onCreateSameLevelSegment?: (parentSegmentId: string) => void;
  onCreateChildSegment?: (parentSegmentId: string) => void;
}

export default function StoryFeed({
  segments,
  isLoading,
  onRefresh,
  onLoadMore,
  onBranchSelect,
  hasMoreSegments,
  showCreationIcons = false,
  onCreateSameLevelSegment,
  onCreateChildSegment,
}: StoryFeedProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  // Ref for the FlatList
  const flatListRef = useRef<FlatList>(null);

  // State for the active branch carousel
  const [activeBranchSegmentId, setActiveBranchSegmentId] = useState<
    string | null
  >(null);

  // Get branch data for the active segment
  const {
    branches,
    totalBranches,
    isLoading: branchesLoading,
    currentPage,
    hasMorePages,
    loadMore: loadMoreBranches,
    changeFilter,
  } = useBranchCarousel({
    segmentId: activeBranchSegmentId || '',
    pageSize: 5,
  });

  // Handle opening the branch carousel
  const handleOpenBranchCarousel = (segmentId: string) => {
    setActiveBranchSegmentId(segmentId);
  };

  // Handle closing the branch carousel
  const handleCloseBranchCarousel = () => {
    setActiveBranchSegmentId(null);
  };

  // Handle selecting a branch
  const handleBranchSelect = async (branchId: string) => {
    handleCloseBranchCarousel();
    await onBranchSelect(branchId);
  };

  // Render a story node card
  const renderStoryNode = ({
    item,
    index,
  }: {
    item: StorySegment;
    index: number;
  }) => {
    const isFirstNode = index === 0;
    const isLastNode = index === segments.length - 1;
    const showBranchCarousel = activeBranchSegmentId === item.id;

    // 使用工具函数获取子分支数量
    const childrenCount = getChildrenCount(item.children_count);
    const hasBranches = childrenCount > 0;

    // 调试日志
    console.log(
      'StoryFeed - segment:',
      item.id,
      'children_count raw:',
      JSON.stringify(item.children_count),
      'processed childrenCount:',
      childrenCount,
      'hasBranches:',
      hasBranches
    );

    return (
      <View>
        <View style={styles.nodeContainer}>
          <View style={{ position: 'relative' }}>
            <StoryNodeCard
              segment={item}
              isOriginStory={isFirstNode}
              onBranchPress={() => {
                console.log(
                  'Branch button pressed for segment:',
                  item.id,
                  'hasBranches:',
                  hasBranches
                );
                // Always open the carousel when the button is clicked, even if there are no branches yet
                handleOpenBranchCarousel(item.id);
              }}
              hasBranches={hasBranches}
            />

            {/* 下级创作按钮 - 显示在段落底部边框中间 */}
            {showCreationIcons && isLastNode && (
              <View
                style={{
                  position: 'absolute',
                  bottom: -1, // 调整到边框线位置
                  left: 0,
                  right: 0,
                  height: 2, // 给予足够的高度
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 10,
                }}
              >
                <FAB
                  size="small"
                  icon="plus"
                  onPress={() => onCreateChildSegment?.(item.id)}
                  style={{
                    backgroundColor: theme.colors.secondary,
                    marginTop: -16, // 向上偏移半个按钮高度，使按钮中心点位于边线上
                  }}
                  customSize={32} // 更小的尺寸以适应精确定位
                />
              </View>
            )}
          </View>

          {/* 同级创作按钮 - 显示在段落右侧中间 */}
          {showCreationIcons && !isFirstNode && (
            <FAB
              size="small"
              icon="plus"
              onPress={() =>
                onCreateSameLevelSegment?.(item.parent_segment_id || '')
              }
              style={{
                position: 'absolute',
                right: -16, // 按钮中心定在边框位置
                top: '50%', // 垂直居中
                marginTop: -16, // 调整垂直居中位置
                backgroundColor: theme.colors.tertiary,
              }}
              customSize={32} // 更小的尺寸以适应精确定位
            />
          )}
        </View>

        {showBranchCarousel && (
          <BranchCarousel
            segmentId={item.id}
            branches={branches}
            totalBranches={totalBranches}
            isLoading={branchesLoading}
            currentPage={currentPage}
            hasMorePages={hasMorePages}
            onBranchSelect={handleBranchSelect}
            onClose={handleCloseBranchCarousel}
            onLoadMore={loadMoreBranches}
            onFilterChange={changeFilter}
          />
        )}
      </View>
    );
  };

  // Render the loading footer
  const renderFooter = () => {
    if (!isLoading) return null;

    return (
      <View
        style={{
          padding: theme.spacing?.lg || 24,
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'row',
        }}
      >
        <ActivityIndicator size="small" />
        <Text
          variant="bodyMedium"
          style={{
            marginLeft: theme.spacing?.sm || 8,
            color: theme.colors.onSurfaceVariant,
          }}
        >
          {t('storyDetail.loadingMore', 'Loading more...')}
        </Text>
      </View>
    );
  };

  // Render the empty state
  const renderEmpty = () => {
    if (isLoading) return null;

    return (
      <View
        style={{
          padding: theme.spacing?.xl || 32 * 2,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Text
          variant="bodyLarge"
          style={{ color: theme.colors.onSurfaceVariant }}
        >
          {t('storyDetail.noSegments', 'No story segments found')}
        </Text>
      </View>
    );
  };

  return (
    <FlatList
      ref={flatListRef}
      data={segments}
      renderItem={renderStoryNode}
      keyExtractor={(item) => item.id}
      contentContainerStyle={{
        paddingHorizontal: theme.spacing?.md || 16,
        paddingVertical: theme.spacing?.md || 16,
        flexGrow: 1,
      }}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={isLoading && segments.length === 0}
          onRefresh={onRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
      onEndReached={() => {
        if (!isLoading && hasMoreSegments) {
          onLoadMore();
        }
      }}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      initialNumToRender={10}
      maxToRenderPerBatch={5}
      windowSize={10}
      removeClippedSubviews={true}
    />
  );
}

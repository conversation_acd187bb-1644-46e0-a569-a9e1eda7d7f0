import React from 'react';
import { FlatList, ViewStyle } from 'react-native';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import StoryCard, { Story } from '@/components/stories/StoryCard';

interface StoryGridProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
  style?: ViewStyle;
}

export function StoryGrid({ stories, onStoryPress, style }: StoryGridProps) {
  const theme = usePaperTheme();

  const renderStoryCard = ({ item }: { item: Story }) => (
    <StoryCard story={item} onPress={() => onStoryPress?.(item.id)} />
  );

  return (
    <FlatList
      data={stories}
      renderItem={renderStoryCard}
      keyExtractor={(item) => item.id}
      numColumns={2}
      style={style}
      contentContainerStyle={{
        padding: theme.spacing?.sm || 8, // MD3 Expressive: 增加内边距
        gap: theme.spacing?.sm || 8, // MD3 Expressive: 使用 gap
      }}
      columnWrapperStyle={{
        justifyContent: 'space-between',
        marginBottom: theme.spacing?.sm || 8, // MD3 Expressive: 行间距
      }}
      showsVerticalScrollIndicator={false}
      scrollEnabled={false} // Disable scroll since it's inside a ScrollView
    />
  );
}

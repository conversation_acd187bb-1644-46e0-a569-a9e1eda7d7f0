import React from 'react';
import { Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Story as ApiStory } from '@/api/stories';
import { FeaturedStory } from './FeaturedStory';
import { StoryListTabs } from './StoryListTabs';
import { StoryGrid } from './StoryGrid';

interface HomeScreenContentProps {
  featuredStory: ApiStory | null;
  stories: ApiStory[];
  storyListTabs: string[];
  activeStoryListTab: string;
  onTabPress: (tab: string) => void;
  onStoryPress: (storyId: string) => void;
}

export function HomeScreenContent({
  featuredStory,
  stories,
  storyListTabs,
  activeStoryListTab,
  onTabPress,
  onStoryPress,
}: HomeScreenContentProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <>
      {/* Featured Story Section - MD3 Expressive Layout */}
      {featuredStory && (
        <>
          <Text
            variant="headlineSmall"
            style={{
              marginTop: theme.spacing?.lg || 24,
              marginBottom: theme.spacing?.md || 16,
              marginHorizontal: theme.spacing?.md || 16,
              color: theme.colors.onSurface,
              fontWeight: '500',
            }}
          >
            {t('homeScreen.sectionTitle.featured', '精选故事')}
          </Text>
          <FeaturedStory
            story={featuredStory}
            onPress={() => onStoryPress(featuredStory.id)}
            style={{
              marginHorizontal: theme.spacing?.md || 16,
              marginBottom: theme.spacing?.lg || 24,
            }}
          />
        </>
      )}

      {/* Story List Section - MD3 Expressive Layout */}
      <StoryListTabs
        tabs={storyListTabs}
        activeTab={activeStoryListTab}
        onTabPress={onTabPress}
        style={{
          marginHorizontal: theme.spacing?.md || 16,
          marginBottom: theme.spacing?.md || 16,
        }}
      />
      <StoryGrid
        stories={stories}
        onStoryPress={onStoryPress}
        style={{
          marginHorizontal: theme.spacing?.md || 16,
        }}
      />
    </>
  );
}

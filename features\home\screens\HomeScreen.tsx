import React from 'react';
import { ScrollView } from 'react-native';
import { Surface, Text, Appbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';
import { mockThemes } from '@/utils/mockData';
import SearchBar from '@/components/ui/SearchBar';
// HeaderBar is now handled by _layout.tsx
import { useRouter } from 'expo-router';

// Import feature components
import { ThemeCarousel } from '@/features/home/<USER>/ThemeCarousel';
import { HomeScreenLoading } from '@/features/home/<USER>/HomeScreenLoading';
import { HomeScreenError } from '@/features/home/<USER>/HomeScreenError';
import { HomeScreenContent } from '@/features/home/<USER>/HomeScreenContent';
import { useHomeScreenData } from '@/features/home/<USER>/useHomeScreenData';

export default function HomeScreen() {
  const theme = usePaperTheme();
  const { t } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = React.useState('');

  const {
    storyListTabs,
    activeStoryListTab,
    setActiveStoryListTab,
    stories,
    featuredStory,
    isLoading,
    error,
    fetchStories,
    mapTabToFilter,
  } = useHomeScreenData();

  const themes = mockThemes;

  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    router.push(`/stories/${storyId}`);
  };

  const handleThemePress = (themeId: string) => {
    console.log('Theme pressed:', themeId);
    // 导航到主题搜索结果
    router.push(`/search?topic=${encodeURIComponent(themeId)}`);
  };

  const handleSearch = (text: string) => {
    if (text.trim()) {
      // 导航到搜索结果页面
      router.push(`/search?q=${encodeURIComponent(text)}`);
    }
  };

  const renderContent = () => {
    if (isLoading && stories.length === 0) {
      return <HomeScreenLoading />;
    }

    if (error) {
      return (
        <HomeScreenError
          error={error}
          onRetry={() => fetchStories(mapTabToFilter(activeStoryListTab))}
        />
      );
    }

    return (
      <HomeScreenContent
        featuredStory={featuredStory}
        stories={stories}
        storyListTabs={storyListTabs}
        activeStoryListTab={activeStoryListTab}
        onTabPress={setActiveStoryListTab}
        onStoryPress={handleStoryPress}
      />
    );
  };

  return (
    <Surface style={{ flex: 1 }}>
      <SafeAreaView style={{ flex: 1 }} edges={['left', 'right', 'bottom']}>
        {/* Header is now handled by _layout.tsx */}

        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            flexGrow: 1,
          }}
        >
          {/* Search Section - MD3 Expressive Layout */}
          <Surface
            style={{
              marginHorizontal: theme.spacing?.md || 16,
              marginTop: theme.spacing?.md || 16,
              marginBottom: theme.spacing?.lg || 24,
              elevation: 0,
            }}
          >
            <SearchBar
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSearch={handleSearch}
              placeholder={t(
                'homeScreen.searchPlaceholder',
                '搜索故事、作者、标签...'
              )}
            />
          </Surface>

          {/* Themes Section - MD3 Expressive Layout */}
          <Surface
            style={{
              marginBottom: theme.spacing?.lg || 24,
              elevation: 0,
            }}
          >
            <Text
              variant="headlineSmall"
              style={{
                marginHorizontal: theme.spacing?.md || 16,
                marginBottom: theme.spacing?.md || 16,
                color: theme.colors.onSurface,
                fontWeight: '500',
              }}
            >
              {t('homeScreen.sectionTitle.themes', '故事主题')}
            </Text>
            <ThemeCarousel themes={themes} onThemePress={handleThemePress} />
          </Surface>

          {/* Content Section - MD3 Expressive Layout */}
          <Surface
            style={{
              flex: 1,
              marginHorizontal: theme.spacing?.md || 16,
              marginBottom: theme.spacing?.lg || 24,
              borderRadius: theme.roundness || 12,
              elevation: 0,
            }}
          >
            {renderContent()}
          </Surface>
        </ScrollView>
      </SafeAreaView>
    </Surface>
  );
}

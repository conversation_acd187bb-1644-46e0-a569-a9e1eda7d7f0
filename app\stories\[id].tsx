import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import StoryFeedScreen from '@/features/stories/screens/StoryFeedScreen'; // Using our new feed-style component
import { Surface, Text } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

export default function StoryDetailPageRoute() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const theme = usePaperTheme();

  if (!id) {
    // This should ideally not happen if navigation is correct
    return (
      <Surface
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Text variant="bodyLarge">Story ID not found.</Text>
      </Surface>
    );
  }

  return (
    <>
      <Stack.Screen options={{ title: 'Story', headerShown: false }} />
      {/* Using our new feed-style component with improved branch visualization */}
      <StoryFeedScreen storyId={id} />
    </>
  );
}

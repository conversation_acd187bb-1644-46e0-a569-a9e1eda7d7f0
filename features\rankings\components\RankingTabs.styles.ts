import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      paddingTop: theme.spacing.md,
    },
    tabsContainer: {
      flexDirection: 'row',
      paddingHorizontal: theme.spacing.md,
    },
    tab: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      alignItems: 'center',
      borderBottomWidth: 2,
      borderBottomColor: 'transparent',
    },
    activeTab: {
      borderBottomColor: theme.colors.primary,
    },
    tabText: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.secondaryText,
    },
    activeTabText: {
      color: theme.colors.primary,
    },
    periodTabsContainer: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
    },
    periodTab: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.xs,
      marginRight: theme.spacing.sm,
      borderRadius: 20, // Use fixed value instead of theme.borderRadius.full
      backgroundColor: theme.colors.background,
    },
    activePeriodTab: {
      backgroundColor: theme.colors.primary,
    },
    periodTabText: {
      fontFamily: theme.fonts.medium,
      fontSize: 14,
      color: theme.colors.secondaryText,
    },
    activePeriodTabText: {
      color: theme.colors.white,
    },
  });

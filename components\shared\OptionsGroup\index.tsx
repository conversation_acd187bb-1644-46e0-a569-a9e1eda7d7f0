import React from 'react';
import { ViewStyle, StyleProp } from 'react-native';
import { SegmentedButtons } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

// Define the structure for each option
export interface Option<T> {
  value: T;
  label: string;
}

interface OptionsGroupProps<T> {
  options: Option<T>[];
  selectedValue: T;
  onSelect: (value: T) => void;
  style?: StyleProp<ViewStyle>; // Optional style prop for the container
}

export function OptionsGroup<T>({
  options,
  selectedValue,
  onSelect,
  style,
}: OptionsGroupProps<T>) {
  const theme = usePaperTheme();

  // Convert options to SegmentedButtons format
  const buttons = options.map((option) => ({
    value: String(option.value), // SegmentedButtons expects string values
    label: option.label,
  }));

  // Handle value change - convert back to original type
  const handleValueChange = (value: string) => {
    const selectedOption = options.find((opt) => String(opt.value) === value);
    if (selectedOption) {
      onSelect(selectedOption.value);
    }
  };

  return (
    <SegmentedButtons
      value={String(selectedValue)}
      onValueChange={handleValueChange}
      buttons={buttons}
      style={[
        {
          marginHorizontal: theme.spacing?.md || 16,
        },
        style,
      ]}
    />
  );
}

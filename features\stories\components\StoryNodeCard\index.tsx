import React from 'react';
import { View, Image } from 'react-native';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Text,
  Avatar,
  IconButton,
  Button,
  Chip,
  Icon,
} from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { StorySegment } from '@/api/stories/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useI18n } from '@/hooks/useI18n';
import { getChildrenCount } from '@/utils/storyHelpers';

interface StoryNodeCardProps {
  segment: StorySegment;
  isOriginStory?: boolean;
  onBranchPress: () => void;
  hasBranches?: boolean;
}

export default function StoryNodeCard({
  segment,
  isOriginStory = false,
  onBranchPress,
  hasBranches: propHasBranches,
}: StoryNodeCardProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();
  const { currentLanguage } = useI18n();

  // Format the relative time (e.g., "2h", "1d")
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const locale = currentLanguage === 'zh' ? zhCN : enUS;
      return formatDistanceToNow(date, { locale, addSuffix: false });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // 使用工具函数获取子分支数量
  const childrenCount = getChildrenCount(segment.children_count);

  // 如果传入了 hasBranches 属性，则使用该属性，否则根据子分支数量决定
  const hasBranches =
    propHasBranches !== undefined ? propHasBranches : childrenCount > 0;

  // 用于显示的子分支数量，使用实际的子分支数量
  const displayChildrenCount = childrenCount;

  console.log(
    'StoryNodeCard - segment:',
    segment.id,
    'parent_segment_id:',
    segment.parent_segment_id,
    'children_count:',
    displayChildrenCount,
    'hasBranches:',
    hasBranches,
    'content:',
    segment.content.substring(0, 30)
  );

  return (
    <Card
      style={{
        marginVertical: theme.spacing?.sm || 8,
        backgroundColor: isOriginStory
          ? theme.colors.primaryContainer
          : theme.colors.surface,
      }}
      elevation={isOriginStory ? 4 : 2}
    >
      {/* Creator Info Row */}
      <Card.Content style={{ paddingBottom: 0 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: theme.spacing?.sm || 8,
          }}
        >
          <Avatar.Image
            size={40}
            source={
              segment.profiles?.avatar_url
                ? { uri: segment.profiles.avatar_url }
                : require('@/assets/images/default-avatar.png')
            }
          />
          <View
            style={{
              flex: 1,
              marginLeft: theme.spacing?.sm || 8,
            }}
          >
            <Text variant="titleSmall" numberOfLines={1}>
              {segment.profiles?.username ||
                t('common.unknownUser', '未知用户')}
            </Text>
            <Text
              variant="bodySmall"
              numberOfLines={1}
              style={{ color: theme.colors.onSurfaceVariant }}
            >
              @{segment.profiles?.username || 'username'} •{' '}
              {formatRelativeTime(segment.created_at)}
            </Text>
          </View>

          <IconButton icon="dots-vertical" size={20} onPress={() => {}} />
        </View>
      </Card.Content>

      {/* Story Content */}
      <Card.Content>
        <Text variant="bodyMedium">{segment.content}</Text>
      </Card.Content>

      {/* Interaction Row */}
      <Card.Actions style={{ justifyContent: 'space-between' }}>
        <View style={{ flexDirection: 'row' }}>
          <Button
            mode="text"
            compact
            icon={() => <Icon source="comment-outline" size={16} />}
            labelStyle={{ fontSize: 12, marginLeft: -4 }}
          >
            0
          </Button>

          <Button
            mode="text"
            compact
            icon={() => <Icon source="thumb-up-outline" size={16} />}
            labelStyle={{ fontSize: 12, marginLeft: -4 }}
          >
            0
          </Button>

          <Button
            mode="text"
            compact
            icon={() => <Icon source="thumb-down-outline" size={16} />}
            labelStyle={{ fontSize: 12, marginLeft: -4 }}
          >
            0
          </Button>

          <Button
            mode="text"
            compact
            icon={() => <Icon source="bookmark-outline" size={16} />}
            labelStyle={{ fontSize: 12, marginLeft: -4 }}
          >
            0
          </Button>
        </View>

        {hasBranches ? (
          <Chip
            mode="elevated"
            icon="source-branch"
            onPress={onBranchPress}
            style={{
              backgroundColor: isOriginStory
                ? theme.colors.primary
                : theme.colors.secondaryContainer,
            }}
            textStyle={{
              color: isOriginStory
                ? theme.colors.onPrimary
                : theme.colors.onSecondaryContainer,
            }}
          >
            {displayChildrenCount}
          </Chip>
        ) : null}
      </Card.Actions>
    </Card>
  );
}

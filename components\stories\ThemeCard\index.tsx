import React from 'react';
import { Chip } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { StoryTheme } from '@/types/story';
import {
  BookOpen,
  Rocket,
  Wand,
  Landmark,
  Heart,
  Search,
  Building,
} from 'lucide-react-native';

// Interface for the theme data passed as prop
export interface Theme {
  id: string;
  name: string;
  icon?: string; // Added icon based on switch usage
  color?: string; // Optional specific background color
}

interface ThemeCardProps {
  theme: Theme; // Use the defined interface
  onPress?: (themeId: string) => void;
}

export default function ThemeCard({
  theme: themeProp,
  onPress,
}: ThemeCardProps) {
  const appTheme = usePaperTheme();

  const getIconName = () => {
    // Map theme icons to Material Community Icons
    switch (themeProp.icon) {
      case 'rocket':
        return 'rocket-launch';
      case 'wand':
        return 'magic-staff';
      case 'search':
        return 'magnify';
      case 'heart':
        return 'heart';
      case 'landmark':
        return 'bank';
      case 'building':
        return 'office-building';
      default:
        return 'book-open-page-variant';
    }
  };

  return (
    <Chip
      mode="elevated"
      icon={getIconName()}
      onPress={() => onPress?.(themeProp.id)}
      style={{
        backgroundColor: themeProp.color || appTheme.colors.primaryContainer,
        marginRight: appTheme.spacing?.sm || 8,
        // MD3 Expressive: 移除底部边距，由父容器的 gap 处理
      }}
      textStyle={{
        color: appTheme.colors.onPrimaryContainer,
        fontWeight: '500', // MD3 Expressive 字重
      }}
      compact={false} // MD3 Expressive: 使用标准尺寸
    >
      {themeProp.name}
    </Chip>
  );
}

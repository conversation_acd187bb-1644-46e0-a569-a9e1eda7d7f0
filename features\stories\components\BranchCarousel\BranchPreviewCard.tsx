import React from 'react';
import { View, Dimensions } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Card, Text, Avatar, Chip, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { StorySegment } from '@/api/stories/types';
import { getChildrenCount, getCountValue } from '@/utils/storyHelpers';

interface BranchPreviewCardProps {
  branch: StorySegment;
  onPress: () => void;
  isActive?: boolean;
}

export default function BranchPreviewCard({
  branch,
  onPress,
  isActive = false,
}: BranchPreviewCardProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  // Screen width for responsive sizing
  const { width } = Dimensions.get('window');
  const cardWidth = width * 0.75; // Card takes 75% of screen width

  // Format content preview
  const getContentPreview = (content: string, maxLength = 120) => {
    if (!content) return '';

    // Remove any markdown or special formatting
    const plainText = content.replace(/\[.*?\]/g, '').trim();

    if (plainText.length <= maxLength) return plainText;
    return plainText.substring(0, maxLength) + '...';
  };

  // Determine if branch has special status
  const getBranchStatus = () => {
    if (branch.is_main_branch) {
      return {
        label: t('storyDetail.mainBranch', '主线'),
        chipColor: theme.colors.primaryContainer,
        textColor: theme.colors.onPrimaryContainer,
      };
    }

    if (branch.is_ai_generated) {
      return {
        label: t('storyDetail.aiGenerated', 'AI 精选'),
        chipColor: theme.colors.secondaryContainer,
        textColor: theme.colors.onSecondaryContainer,
      };
    }

    // Check if branch is new (less than 24 hours old)
    const createdAt = new Date(branch.created_at);
    const now = new Date();
    const isNew = now.getTime() - createdAt.getTime() < 24 * 60 * 60 * 1000;

    if (isNew) {
      return {
        label: t('storyDetail.new', '新'),
        chipColor: theme.colors.tertiaryContainer,
        textColor: theme.colors.onTertiaryContainer,
      };
    }

    return null;
  };

  const branchStatus = getBranchStatus();

  return (
    <Card
      style={{
        width: cardWidth,
        marginHorizontal: theme.spacing?.xs || 4,
        backgroundColor: isActive
          ? theme.colors.primaryContainer
          : theme.colors.surface,
      }}
      elevation={isActive ? 8 : 2}
      onPress={onPress}
    >
      {/* Content Preview */}
      <Card.Content>
        <Text variant="bodyMedium" numberOfLines={4}>
          {getContentPreview(branch.content)}
        </Text>
      </Card.Content>

      {/* Author Info */}
      <Card.Content style={{ paddingTop: 0 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: theme.spacing?.sm || 8,
          }}
        >
          <Avatar.Image
            size={24}
            source={
              branch.profiles?.avatar_url
                ? { uri: branch.profiles.avatar_url }
                : require('@/assets/images/default-avatar.png')
            }
          />
          <Text
            variant="labelMedium"
            numberOfLines={1}
            style={{
              marginLeft: theme.spacing?.xs || 4,
              color: theme.colors.onSurface,
            }}
          >
            {branch.profiles?.username || t('common.unknownUser', '未知用户')}
          </Text>
        </View>

        {/* Footer with Stats and Status */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <View style={{ flexDirection: 'row' }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginRight: theme.spacing?.sm || 8,
              }}
            >
              <Icon source="comment-outline" size={14} />
              <Text
                variant="labelSmall"
                style={{
                  marginLeft: theme.spacing?.xs || 4,
                  color: theme.colors.onSurfaceVariant,
                }}
              >
                {getCountValue(branch.comment_count)}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Icon source="source-branch" size={14} />
              <Text
                variant="labelSmall"
                style={{
                  marginLeft: theme.spacing?.xs || 4,
                  color: theme.colors.onSurfaceVariant,
                }}
              >
                {getChildrenCount(branch.children_count)}
              </Text>
            </View>
          </View>

          {branchStatus && (
            <Chip
              mode="flat"
              style={{
                backgroundColor: branchStatus.chipColor,
              }}
              textStyle={{
                color: branchStatus.textColor,
                fontSize: 10,
              }}
            >
              {branchStatus.label}
            </Chip>
          )}
        </View>
      </Card.Content>
    </Card>
  );
}

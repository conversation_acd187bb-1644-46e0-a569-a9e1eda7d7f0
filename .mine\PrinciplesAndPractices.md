## 开发原则与最佳实践

1.  **技术栈严格遵守**:

    - **Expo SDK**: `@latest` (目前为 SDK 53)。
    - **React Native**: `@latest` (目前为 0.79, 配合 React 19)。
    - **TypeScript**: 必须启用 `strict` 模式。所有代码均使用 TypeScript。
    - **路由**: `expo-router @latest`。
    - **状态管理**: `zustand`。按领域或功能划分 store，避免单个过大的全局 store。
    - **样式**: `react-native-paper @latest`。样式定义遵循 react-native-paper 的核心概念。

    - **国际化**: `i18next` 和 `react-i18next`。所有面向用户的文本都应通过 `t` 函数进行翻译。语言文件存放于 `translations/`。
    - **包管理**: `pnpm @latest` (Berry)。
    - **Expo Workflow**: 严格遵循 **Managed Workflow**。
    - **Expo 优先**: 优先使用 Expo 官方提供的库和 API (如 `expo-image` 替代 `react-native-fast-image`, `expo-secure-store` 等)。
    - **兼容性**: 确保所有新增依赖与当前 Expo SDK 版本兼容。

2.  **编码规范**:

    - **函数式编程**: 全面采用函数组件和 React Hooks。**避免使用类组件**。
    - **React Native 新架构**: Fabric 已默认启用。关注其带来的性能优势和潜在的兼容性问题。
    - **Hermes 引擎**: 默认启用，确保代码在其上高效运行。
    - **Metro 优化**: 利用 Metro 的快速解析能力，配置好路径别名等。
    - **代码风格**:
      - 遵循 ESLint 和 Prettier (如果项目配置了) 的规则。
      - 组件命名: `PascalCase` (如 `UserProfileCard.tsx`)。
      - Hooks 命名: `useCamelCase` (如 `useUserDetails.ts`)。
      - 常量命名: `UPPER_SNAKE_CASE`。
    - **文件行数限制 (File Length Limits)**:
      - 单个文件代码行数应尽量控制在 150 行以内，便于阅读和维护。
      - 当文件行数超过 150 行时，应主动考虑重构（例如，拆分组件、提取 hooks、分离业务逻辑等）。
      - 文件行数不应超过 200 行，超过此限制必须进行重构。
    - **路径别名**: 统一使用 `tsconfig.json` 和 `babel.config.js` 中配置的路径别名进行模块导入 (如 `@/components/ui/Button`, `@/features/profile/components/ProfileHeader`)。
    - **导入顺序**:
      1.  React 及 React Native 导入
      2.  第三方库导入
      3.  绝对路径项目模块导入 (按层级 `@/lib`, `@/hooks`, `@/components`, `@/features` 等)
      4.  相对路径导入
    - **组件设计**:
      - **单一职责**: 组件应尽可能小而专注。
      - **高内聚低耦合**: 组件内部逻辑紧密相关，与其他组件依赖尽可能少。
      - **Props 定义**: 清晰、准确地定义 Props 类型。避免使用 `any`。
      - **可复用性**: 优先封装可复用的 `components/ui` (可基于 react-native-paper 提供的原子组件进行自定义封装) 和 `components/shared` 组件。功能特定组件放在对应 `features/<feature-name>/components` 下。
    - **样式 (react-native-paper)**:
      - **核心**: 全面采用 `react-native-paper` 的主题系统进行样式管理，替代传统的 `StyleSheet`。
      - **新组件与页面开发**: 创建新的复杂组件和页面时，**必须**使用 `react-native-paper` 组件进行搭建，并严格遵守 **Material Design 3 (MD3) Expressive 风格**和设计系统，以保持技术栈的统一性和视觉一致性。
      - **`PaperProvider`**: 应用的根组件必须包裹在 `PaperProvider` 中，并通过其 `theme` prop 提供全局主题。
      - **`useTheme` Hook**: 在函数组件中，使用 `useTheme()` hook (推荐创建类型化的 `useAppTheme` hook) 来访问和使用主题属性。
      - **主题驱动样式**: 组件的视觉表现主要由主题中的 `colors`, `fonts`, `roundness` 等属性决定。
      - **组件 Props**: 利用 `react-native-paper` 组件自带的 props (如 `Button` 的 `mode`, `Text` 的 `variant`) 来控制其外观和行为。
      - **`style` Prop 结合主题**: 对于个别组件实例的特定样式调整，可以使用 `style` prop，并结合从 `useTheme()` 获取的主题值。
      - **定制化**:
        - **组件内 `theme` Prop**: 为单个 `react-native-paper` 组件传递 `theme` prop 可以覆盖其从全局主题继承的特定属性。
        - **封装组件**: 若需创建在应用内具有统一自定义样式的组件版本，应创建自定义封装组件。
      - **亮/暗模式**: 主题应支持亮色和暗色模式，并能根据应用设置或系统偏好进行切换。
      - **Material Design 版本**: 可在主题中指定使用 Material Design 2 或 3 (`version: 2` 或 `version: 3`)。
      - **包体积优化**: 在 `babel.config.js` 中为生产环境配置 `react-native-paper/babel` 插件，以减小应用打包体积。
        ```json
        // babel.config.js 示例
        // module.exports = function (api) {
        //   api.cache(true);
        //   return {
        //     presets: ['babel-preset-expo'],
        //     env: {
        //       production: {
        //         plugins: ['react-native-paper/babel'],
        //       },
        //     },
        //   };
        // };
        ```
    - **代码注释**:
      - 为复杂的逻辑、非显而易见的实现、以及公开的 API (如 Hooks, Services) 编写清晰的 JSDoc 注释。
      - `// TODO:` 或 `// FIXME:` 应用于临时代码或已知问题。
    - **错误处理**:
      - 对 API 调用、异步操作等进行充分的错误处理。
      - 向用户展示友好的错误提示。
      - 考虑使用 Error Boundary。
    - **日志记录**: 在关键路径和错误处理中使用适当的日志记录。

3.  **状态管理 (Zustand)**:

    - **全局状态**: 存放于 `lib/store/`，例如 `settingsStore.ts`。仅用于真正全局共享的状态。
    - **功能状态**: 如果某个功能模块（如 `features/creation`）有复杂的状态逻辑，可以在 `features/creation/store/` 下创建该功能专属的 store。
    - **Selectors**: 使用 selectors 优化性能，避免不必要的重渲染。

4.  **国际化 (i18next)**:

    - 键名 (key): 采用结构化或语义化的方式命名，例如 `profile.editButton` 或 `common.submit`。
    - 新文本: 及时添加到 `translations/en.json` 和 `translations/zh.json`。

5.  **API 交互 (Supabase 及其他)**:

    - **封装**: API 调用逻辑应封装在 `api/` 目录下或 `features/<feature-name>/services/` 中。
    - **类型安全**: 为 API 的请求参数和响应数据定义 TypeScript 类型。
    - **加载与错误状态**: UI 组件应能优雅处理 API 调用的加载中、成功和失败状态。

6.  **性能优化**:

    - **列表渲染**: 对长列表使用 `FlatList` 或 `SectionList`，并正确使用 `keyExtractor`、`getItemLayout` (如果适用)以及 `memo`。
    - **图片**: 使用 `expo-image` 并考虑其 `placeholder` 和 `transition` 等属性。优化图片大小和格式。
    - **避免不必要的渲染**: 使用 `React.memo`、`useMemo`、`useCallback`。
    - **Bundle 大小**: 关注应用打包后的大小，移除未使用的代码和资源。

7.  **测试**:
